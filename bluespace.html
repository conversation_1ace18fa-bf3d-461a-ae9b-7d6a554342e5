<!DOCTYPE html>
<html>
  
  <head>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap">
  <style>
    body{
      font-family: 'Montserrat', sans-serif;
      color: aliceblue;
      background-color: black;

    }
    .bottom-section{
      display: flex;
      margin-top: 65px;
      padding: 24px;
      height: 75vh;
      align-items: center;
    }

    .top-section{
      letter-spacing: 5px;
      display: flex;
      gap: -10px;
      padding: 10px;
      padding-left:24px ;
      color: aliceblue;
      font-weight: bolder;
      font-size: larger;
      margin-bottom: 40px;
      align-items: center;
    }

    .sidebar{
      margin-right: 80px;
      top:160px;
      height: 600px;
      align-items:flex-start;
      width: 70px;
      padding: 10px 0px 10px 10px;
      border-color: rgba(255, 255, 255, .2);
      display: flex;
      flex-direction: column;
      gap: 100px; 
      justify-content: center;
      background-image: linear-gradient(black 33%, rgba(255,255,255,0) 0%);
      background-position: right;
      background-size: 1px 3px;
      background-repeat: repeat-y;
    }

    .B{
      width: 60px;
      height: 60px;
      position: relative;
      right: -40px;
      border: 0;
      font-weight: normal;
      font-size: 6px;
      background-color: transparent;
      color: aliceblue;
      font-size: medium;
    }

    .btn-text {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(100%, -50%);
      font-weight: bolder;
      letter-spacing: .5px;
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      white-space: nowrap;
      width: 75px;
      height: 8px;
    }

    .icon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: var(--text-color);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .B:hover .icon {
      transform: scale(0);
    }

    .B:focus .icon {
      transform: scale(0);

    }

    .B:focus .btn-text, .B:hover .btn-text {
      opacity: 1;
      transform: translate(-50%, -50%);
    }

    .B:focus .btn-text{
      width: 75px;
      background-color: #0f33ff;
    }
    
    h1{
      margin: 0;
    }
    .card-grid{
      transition: all .5s;
      display: flex;
      gap: 15px;
      flex-direction: column;
      padding: 24px;
      justify-content: center;
      width: 350px;
    }
    .card-header{
      width: 50px;
      height: 50px;
      background-color: #0f33ff;
      border-radius: 100px;
    }
    .card{
     transition: box-shadow .5s,transform .5s;
     border: 5px  solid aliceblue; width: 300px; padding: 15px;
    }

    .card:hover{
      cursor: pointer;
      box-shadow: 5px 5px 0 #0f33ff;
      transform: translate(1px, 1px) rotate(2deg) scale(1.05);
    }

    .description{
    /* From https://css.glass */
    background: rgba(32, 32, 32, 0.05);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, .05);
    }
    .image{
      position: relative;
      top:52px;
      right: 270px;
      z-index: -1
    }

  </style>

</head>
  <body>
    <div class = "top-section">
      <div class="header-section">
        <h1 style="background-color: #0f33ff; height: 20px;">BLUESPACE</h1>
      </div>
      <div class ="fas fa-user-astronaut" style="position: absolute; right: 50px;top: 30px; font-size: 30px;"></div>
    </div>

    <div class="bottom-section">
      <div class="sidebar">
        <button class="B" id='default-open' onclick="opentab(event,'compare')">
          <i class="fas fa-compress-alt icon"></i>
          <b class="btn-text">Compare</b>
        </button>
        <button class="B" onclick="opentab(event,'pdf')">
          <i class="fas fa-file icon"></i>
          <b class="btn-text">PDF</b>
        </button>
        <button class="B" onclick="opentab(event,'generic')">
          <i class="fas fa-cog icon"></i>
          <b class="btn-text">Generic</b>
        </button>
        <button class="B" onclick="opentab(event,'part-no')">
          <i class="fas fa-microchip icon"></i>
          <b class="btn-text">Part No.</b>
        </button>
      </div>

      <!--Compare--> 
      <div id = "compare" class="card-grid">
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Catalog&nbspCompare</h2>
          <p>compare pdfs with large count of pages</p>
        </div>

        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Equal&nbspManual</h2>
          <p>compare pdfs with large count of pages</p>
        </div>

        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Magic</h2>
          <p>compare pdfs with large count of pages</p>
        </div>

        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Supplier&nbspConfiguration</h2>
          <p>compare pdfs with large count of pages</p>
        </div>

      </div>
      <!--PDF-->
      <div id = "pdf" class="card-grid">
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Inroduction&nbspDate</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
      </div>

      <!--Generic-->
      <div id = "generic" class="card-grid">
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Keyword&nbspsearch</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Extractor</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">metadata</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
      </div>

      <!--Generic-->
      <div id = "part-no" class="card-grid">
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Keyword&nbspsearch</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">Extractor</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
        <div class="card" >
          <h2 class= "card-header" style="margin: 0;">metadata</h2>
          <p>compare pdfs with large count of pages</p>
        </div>
      </div>




      <div style="width: 500px;padding: 50px;text-transform: uppercase; letter-spacing: .5px;font-size: 50px;font-weight:bolder;display: flex; gap:50px; border-bottom: 2px solid rgba(255, 255, 255, .3);">
        <h3 style="background-color: #0f33ff; width:300px; height: 300px; border-radius: 100%; outline: 20px solid black; outline-offset: -150px;">Your all in one document processing tool</h3>
        <img src="Thumbnails/introduction-visual.png" class="image">
      </div>
      <div class="description" style="width: 400px; height: 600px; padding: 24px; margin-left: 50px;">
        <h2>Description</h2>
        <p>
          This is a place holder for tool description of what it to do and how it works and the expected output
        </p>
      </div>
    </div>

    <script>
      function opentab(evt, cityName) {
        // Declare all variables
        var i, tabcontent, tablinks;

        // Get all elements with class="tabcontent" and hide them
        tabcontent = document.getElementsByClassName("card-grid");
        for (i = 0; i < tabcontent.length; i++) {
          tabcontent[i].style.display = "none";
        }

        tablinks = document.getElementsByClassName("B");
        for (i = 0; i < tablinks.length; i++) {
          tablinks[i].className = tablinks[i].className.replace(" active", "");
        }

        // Show the current tab, and add an "active" class to the button that opened the tab
        document.getElementById(cityName).style.display = "flex";
        evt.currentTarget.className += " active";
        console.log(evt.currentTarget.className)

      }

      document.getElementById("default-open").click();
    </script>
    
  </body> 
</html>
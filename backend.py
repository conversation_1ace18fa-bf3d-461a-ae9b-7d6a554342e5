from flask import Flask, request, jsonify, render_template, session, redirect, url_for
import subprocess
import os
import datetime
import json
import tempfile
from time import sleep
import sys
import hashlib
import csv
from functools import wraps


app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'  # Change this in production

# User data file
USERS_FILE = 'users.txt'
# Session tracking file
SESSIONS_FILE = 'sessions.txt'

# Tool mapping
TOOL_MAPPING = {
    'extractor': 'T001',
    'compare': 'T002',
    'generic': 'T003',
    'keyword_search': 'T004',
    'part_search': 'T005',
    'equal_manual': 'T006',
    'magic': 'T007',
    'image_compare': 'T008',
    'catalogue_compare': 'T009'
}

# Reverse mapping for tool ID to name
TOOL_ID_TO_NAME = {
    'T001': 'extractor',
    'T002': 'compare',
    'T003': 'generic',
    'T004': 'keyword_search',
    'T005': 'part_search',
    'T006': 'equal_manual',
    'T007': 'magic',
    'T008': 'image_compare',
    'T009': 'catalogue_compare'
}

def generate_user_id(username):
    """Generate consistent user ID from username"""
    import hashlib
    hash_obj = hashlib.md5(username.encode())
    hash_hex = hash_obj.hexdigest()
    return 'U' + hash_hex[:6].upper()

def get_tool_id(tool_name):
    """Get tool ID from tool name"""
    return TOOL_MAPPING.get(tool_name, tool_name)

def get_tool_name(tool_id):
    """Get tool name from tool ID"""
    return TOOL_ID_TO_NAME.get(tool_id, tool_id)

# Global variables for tool processing
active_sessions = {}  # Dictionary to store session data

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login_page'))
        return f(*args, **kwargs)
    return decorated_function

# User management functions
def hash_password(password):
    """Hash a password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def load_users():
    """Load users from text file"""
    users = {}
    if os.path.exists(USERS_FILE):
        try:
            with open(USERS_FILE, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) >= 3:
                        username, email, password_hash = row[0], row[1], row[2]
                        users[username] = {
                            'email': email,
                            'password_hash': password_hash
                        }
        except Exception as e:
            print(f"Error loading users: {e}")
    return users

def save_user(username, email, password):
    """Save a new user to text file"""
    password_hash = hash_password(password)

    # Check if user already exists
    users = load_users()
    if username in users:
        return False, "Username already exists"

    # Check if email already exists
    for user_data in users.values():
        if user_data['email'] == email:
            return False, "Email already exists"

    # Append new user to file
    try:
        with open(USERS_FILE, 'a', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([username, email, password_hash])
        return True, "User created successfully"
    except Exception as e:
        return False, f"Error saving user: {e}"

def verify_user(username, password):
    """Verify user credentials"""
    users = load_users()
    if username in users:
        password_hash = hash_password(password)
        if users[username]['password_hash'] == password_hash:
            return True, users[username]
    return False, None

# Session tracking functions
def generate_session_id():
    """Generate a unique session ID"""
    import uuid
    return str(uuid.uuid4())[:8]

def create_session(user_id, tool_id):
    """Create a new session entry"""
    session_id = generate_session_id()
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Session format: session_id,user_id,tool_id,status,progress,timestamp
    session_data = f"{session_id},{user_id},{tool_id},in_progress,0,{timestamp}\n"

    with open(SESSIONS_FILE, 'a', encoding='utf-8') as f:
        f.write(session_data)

    return session_id

def update_session_progress(session_id, progress, status=None):
    """Update session progress"""
    if not os.path.exists(SESSIONS_FILE):
        return False

    sessions = []
    updated = False

    with open(SESSIONS_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split(',')
                if len(parts) >= 6 and parts[0] == session_id:
                    # Update progress and status
                    if status:
                        parts[3] = status
                    parts[4] = str(progress)
                    parts[5] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    updated = True
                sessions.append(','.join(parts))

    if updated:
        with open(SESSIONS_FILE, 'w', encoding='utf-8') as f:
            for session_line in sessions:
                f.write(session_line + '\n')

    return updated

def get_user_sessions(user_id):
    """Get all sessions for a specific user"""
    if not os.path.exists(SESSIONS_FILE):
        return {'in_progress': [], 'finished': []}

    in_progress = []
    finished = []

    with open(SESSIONS_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split(',')
                if len(parts) >= 6 and parts[1] == user_id:
                    session_data = {
                        'session_id': parts[0],
                        'user_id': parts[1],
                        'tool_id': parts[2],
                        'status': parts[3],
                        'progress': int(parts[4]),
                        'timestamp': parts[5]
                    }

                    if parts[3] == 'done' or int(parts[4]) >= 100:
                        finished.append(session_data)
                    else:
                        in_progress.append(session_data)

    return {'in_progress': in_progress, 'finished': finished}
# Authentication routes
@app.route('/login')
def login_page():
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    remember_me = data.get('remember_me', False)

    if not username or not password:
        return jsonify({'success': False, 'message': 'Username and password are required'})

    is_valid, user_data = verify_user(username, password)

    if is_valid:
        user_id = generate_user_id(username)
        session['user_id'] = user_id
        session['username'] = username
        session['user_email'] = user_data['email']
        if remember_me:
            session.permanent = True
        return jsonify({'success': True, 'message': 'Login successful'})
    else:
        return jsonify({'success': False, 'message': 'Invalid username or password'})

@app.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    username = data.get('username')
    email = data.get('email')
    password = data.get('password')

    if not username or not email or not password:
        return jsonify({'success': False, 'message': 'All fields are required'})

    if len(password) < 6:
        return jsonify({'success': False, 'message': 'Password must be at least 6 characters long'})

    success, message = save_user(username, email, password)
    return jsonify({'success': success, 'message': message})

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login_page'))

@app.route('/api/sessions')
@login_required
def get_sessions():
    """Get user sessions"""
    user_id = session['user_id']
    sessions = get_user_sessions(user_id)
    return jsonify(sessions)

@app.route('/api/sessions/<session_id>/progress')
@login_required
def get_session_progress(session_id):
    """Get specific session progress"""
    user_id = session['user_id']
    sessions = get_user_sessions(user_id)

    # Find the session
    for session_data in sessions['in_progress'] + sessions['finished']:
        if session_data['session_id'] == session_id:
            return jsonify(session_data)

    return jsonify({'error': 'Session not found'}), 404

@app.route('/')
@login_required
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
@login_required
def upload():
    current_time = datetime.datetime.now().strftime("%d-%m-%Y_%H%M%S")
    input_dict = {}

    uploaded_file = request.files['file']
    tool_id = request.form['tool']  # Now receiving tool ID directly
    mode = request.form['mode']
    user_id = session['user_id']

    # Get tool name from tool ID for file paths
    tool_name = get_tool_name(tool_id)

    # Create a new session
    session_id = create_session(user_id, tool_id)

    output_path = f"AutoSpace_output_files\{tool_name}_{current_time}_output.txt"
    tool_path = f"python_tools/{tool_name}.py"

    uploaded_file_content = uploaded_file.read().decode('utf-8', errors='ignore')

    for id, row in enumerate(uploaded_file_content.split("\n"), 1):
        input_dict[id] = row

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_json:
        json.dump(input_dict, temp_json)
        temp_json_path = temp_json.name

    # Store session data for processing
    active_sessions[session_id] = {
        'tool_path': tool_path,
        'temp_json_path': temp_json_path,
        'output_path': output_path,
        'mode': mode,
        'user_id': user_id,
        'tool_id': tool_id
    }

    return jsonify({'message': 'success', 'session_id': session_id})


@app.route('/runtool', methods=['POST'])
@login_required
def runtool():
    data = request.get_json()
    session_id = data.get('session_id')

    if not session_id or session_id not in active_sessions:
        return jsonify({'error': 'Invalid session ID'}), 400

    # Start processing in a separate thread to avoid blocking
    import threading
    thread = threading.Thread(target=process_session, args=(session_id,))
    thread.daemon = True
    thread.start()

    return jsonify({'message': 'Processing started', 'session_id': session_id})

def process_session(session_id):
    """Process a session in a separate thread"""
    session_data = active_sessions.get(session_id)
    if not session_data:
        return

    tool_path = session_data['tool_path']
    temp_json_path = session_data['temp_json_path']
    output_path = session_data['output_path']
    mode = session_data['mode']

    current_path = os.getcwd()
    output_path = os.path.join(current_path, output_path)
    total_output = 0

    status = 'running'

    # Get initial input count for progress calculation
    with open(temp_json_path, 'r') as f:
        initial_input_dict = json.load(f)
    total_input_count = len(initial_input_dict)

    # Update session to running status
    update_session_progress(session_id, 5, 'running')

    try:
        while True:

            if os.stat(temp_json_path).st_size == 2:
                # Mark as completed
                update_session_progress(session_id, 100, 'done')
                break

            if status == 'finished':
                update_session_progress(session_id, 100, 'done')
                break

            process = subprocess.Popen(
            ["python", "-u", tool_path, temp_json_path, output_path, mode])

            with open(temp_json_path, 'r') as f:
                input_dict = json.load(f)

            # Calculate and update progress
            remaining_items = len(input_dict)
            processed_items = total_input_count - remaining_items
            progress = min(95, int((processed_items / total_input_count) * 100)) if total_input_count > 0 else 5
            update_session_progress(session_id, progress)

            sleep(5)

            while True:
                old_file_size = os.stat(output_path).st_size

                sleep(5)
                if os.stat(temp_json_path).st_size == 2:
                    status = 'finished'
                    update_session_progress(session_id, 100, 'done')
                    break

                if os.stat(output_path).st_size == old_file_size:
                        process.terminate()

                        with open(output_path, 'r', encoding='utf8') as f:
                            lines = f.readlines()

                        loop_finished_rows = len(lines) - total_output
                        residual_ids = dict(list(input_dict.items())[loop_finished_rows+1:])

                        with open(temp_json_path, 'w') as f:
                            json.dump(residual_ids, f)

                        total_output = len(lines)

                        # Update progress based on remaining items
                        remaining_items = len(residual_ids)
                        processed_items = total_input_count - remaining_items
                        progress = min(95, int((processed_items / total_input_count) * 100)) if total_input_count > 0 else 5
                        update_session_progress(session_id, progress)

                        print("input_file updated and process restarted")
                        break
    except Exception as e:
        print(f"Error processing session {session_id}: {e}")
        update_session_progress(session_id, 0, 'error')
    finally:
        # Clean up session data
        if session_id in active_sessions:
            del active_sessions[session_id]

if __name__ == '__main__':
    app.run(host='0.0.0.0',port='5000', debug=True)
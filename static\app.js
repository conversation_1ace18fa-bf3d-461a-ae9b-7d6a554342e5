// Date(year, month, day, hour, minute, second, ms)

// Clear localStorage on page load
localStorage.clear();

// DOM Elements
const navTabs = document.querySelectorAll('.tab');
const toolsSection = document.querySelector('.tools-content');
const descriptionBox = document.querySelector('.description-box');
const runButton = descriptionBox.querySelector('.run-button');
const uploadButton = descriptionBox.querySelector('.upload-button');
const downloadSampleButton = descriptionBox.querySelector('.download-sample-button');
const dButtons = document.querySelectorAll('.d-buttons');
const fileInput = document.getElementById('file-input');
const modeButton = document.querySelector('.mode-button');
const active_sessions = document.querySelector('.progress-box');
const finished_sessions = document.querySelector('.finished-box');

// Global variables
let uploadedFile = null;
let selectedTool = null;
let activeSessions = new Map(); // Track active sessions for progress updates

// File input change handler
fileInput.addEventListener('change', () => {
  const file = fileInput.files[0];
  if (file) uploadedFile = file;
});

// Tab click status tracking
const tabClickStatus = {
  pdf: false,
  compare: false,
  generic: false,
  configuration: false,
};

// Show performance information
function showPerformance() {
  const progressBox = document.querySelector('.visualization-box');
  const systemInfoList = document.createElement('ul');

  systemInfoList.innerHTML = `
    <li>Total Device Memory: ${navigator.deviceMemory}</li>
    <li>Hardware Concurrency: ${navigator.hardwareConcurrency}</li>
    <li>Platform: ${navigator.platform}</li>
  `;

  progressBox.appendChild(systemInfoList);
}

// Import tool mapping functions
import { getToolNameById, getToolsByCategory } from './data.js';

// Session management functions
function createSessionCard(sessionData) {
  const session_card = document.createElement('div');
  session_card.className = 'session-card';
  session_card.id = sessionData.session_id;

  const time = new Date(sessionData.timestamp);
  const str_time = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;

  // Get tool name from tool ID
  const toolName = getToolNameById(sessionData.tool_id);

  // Determine status class for border coloring
  let statusClass = 'status-pending';

  if (sessionData.status === 'done' || sessionData.progress >= 100) {
    statusClass = 'status-done';
  } else if (sessionData.status === 'running') {
    statusClass = 'status-running';
  } else if (sessionData.status === 'error') {
    statusClass = 'status-error';
  }

  // Add status class to session card
  session_card.classList.add(statusClass);

  session_card.innerHTML = `
    <div class="session-header">
      <span class="tool-name">${toolName.toUpperCase()}</span>
      <span class="session-status">${sessionData.status.toUpperCase()}</span>
    </div>
    <div class="session-info">
      <span class="session-time">${str_time}</span>
      <span class="session-id">ID: ${sessionData.session_id}</span>
    </div>
    <div class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" style="width: ${sessionData.progress}%"></div>
      </div>
      <span class="progress-text">${sessionData.progress}%</span>
    </div>
    ${sessionData.status === 'done' ? '<span class="click-copy">Click to copy session ID</span>' : ''}
  `;

  if (sessionData.status === 'done') {
    session_card.addEventListener('click', () => {
      session_card.querySelector('.click-copy').textContent = 'Copied!';
      navigator.clipboard.writeText(sessionData.session_id);
      setTimeout(() => {
        session_card.querySelector('.click-copy').textContent = 'Click to copy session ID';
      }, 2000);
    });
  }

  return session_card;
}

function updateSessionCard(sessionData) {
  const existingCard = document.getElementById(sessionData.session_id);
  if (existingCard) {
    // Remove old status classes
    existingCard.classList.remove('status-pending', 'status-running', 'status-done', 'status-error');

    const newCard = createSessionCard(sessionData);
    existingCard.parentNode.replaceChild(newCard, existingCard);

    // Move to finished section if completed
    if ((sessionData.status === 'done' || sessionData.progress >= 100) && newCard.parentNode === active_sessions) {
      active_sessions.removeChild(newCard);
      finished_sessions.appendChild(newCard);
    }
  }
}

function addSessionCard(sessionData) {
  const session_card = createSessionCard(sessionData);

  if (sessionData.status === 'done' || sessionData.progress >= 100) {
    finished_sessions.appendChild(session_card);
  } else {
    active_sessions.appendChild(session_card);
    // Start progress tracking for active sessions
    if (sessionData.status !== 'error') {
      activeSessions.set(sessionData.session_id, sessionData);
      startProgressTracking(sessionData.session_id);
    }
  }
}

// Progress tracking functions
function startProgressTracking(sessionId) {
  const interval = setInterval(async () => {
    try {
      // Only check if session is still in activeSessions map
      if (!activeSessions.has(sessionId)) {
        clearInterval(interval);
        return;
      }

      const response = await fetch(`/api/sessions/${sessionId}/progress`);
      if (response.ok) {
        const sessionData = await response.json();
        updateSessionCard(sessionData);

        // Stop tracking if session is complete
        if (sessionData.status === 'done' || sessionData.progress >= 100) {
          clearInterval(interval);
          activeSessions.delete(sessionId);
        }
      } else {
        clearInterval(interval);
        activeSessions.delete(sessionId);
      }
    } catch (error) {
      console.error('Error tracking progress:', error);
      clearInterval(interval);
      activeSessions.delete(sessionId);
    }
  }, 5000); // Check every 5 seconds
}

async function loadUserSessions() {
  try {
    const response = await fetch('/api/sessions');
    if (response.ok) {
      const sessions = await response.json();

      // Clear existing sessions
      active_sessions.innerHTML = '';
      finished_sessions.innerHTML = '';
      activeSessions.clear();

      // Add in-progress sessions
      sessions.in_progress.forEach(sessionData => {
        addSessionCard(sessionData);
      });

      // Add finished sessions
      sessions.finished.forEach(sessionData => {
        addSessionCard(sessionData);
      });

      // If no active sessions, stop the periodic refresh
      if (sessions.in_progress.length === 0) {
        console.log('No active sessions, stopping periodic refresh');
        // Clear any existing refresh interval
        if (window.sessionRefreshInterval) {
          clearInterval(window.sessionRefreshInterval);
          window.sessionRefreshInterval = null;
        }
      } else {
        // Start periodic refresh if there are active sessions and it's not already running
        if (!window.sessionRefreshInterval) {
          console.log('Starting periodic refresh for active sessions');
          window.sessionRefreshInterval = setInterval(loadUserSessions, 30000);
        }
      }
    }
  } catch (error) {
    console.error('Error loading sessions:', error);
  }
}

// Tab navigation event listeners
navTabs.forEach((tab) => {
  tab.addEventListener('click', () => {
    navTabs.forEach((t) => t.classList.remove('active'));
    tab.classList.add('active');

    toolsSection.innerHTML = '';
    descriptionBox.childNodes.forEach((child) => {
        if (child.childNodes){
            child.childNodes.forEach((child) => {
                child.textContent = '';
            });
        }
        else{
            child.textContent = '';
        }
    });
    dButtons.forEach((button) => {
        button.classList.remove('show');
    });

    // Get tools for the selected category
    const categoryTools = getToolsByCategory(tab.id);

    categoryTools.forEach((tool) => {
      const toolCard = document.createElement('div');
      toolCard.className = 'tool-card box';
      toolCard.innerHTML = `
        <span>
          <h3>${tool.name}</h3>
        </span>
      `;

      toolCard.style.cursor = 'pointer';
      toolCard.style.textTransform = 'uppercase';

      toolCard.addEventListener('click', () => {
        // Update description content
        descriptionBox.querySelector('.tool-description').innerHTML = tool.description.replace(/\n/g, '<br>');

        // Update tool name in the AutoSpace header
        document.querySelector('.autospace-header .tool-name').innerHTML = tool.name;

        // Update button text
        downloadSampleButton.textContent = 'Download Sample'
        runButton.textContent = 'Run';
        uploadButton.textContent = 'Upload';

        selectedTool = tool.id;

        // Show all buttons
        dButtons.forEach((button) => {
          button.classList.add('show');
          button.id = selectedTool;
        });

      });
      toolsSection.appendChild(toolCard);
    });
  });
});

// Run Button Click Handler
runButton.addEventListener('click', async () => {
  if (!uploadedFile) {
    alert('Please upload a file');
    return;
  }

  if (!selectedTool) {
    alert('Please select a tool');
    return;
  }

  // Disable run button temporarily to prevent double-clicks
  runButton.disabled = true;
  runButton.textContent = 'Processing...';

  const formData = new FormData();
  formData.append('file', uploadedFile);
  formData.append('tool', selectedTool);
  formData.append('mode', modeButton.checked ? 'fast' : 'normal');

  try {
    // Upload file and create session
    const uploadResponse = await fetch('/upload', {
      method: 'POST',
      body: formData,
    });
    const uploadData = await uploadResponse.json();

    if (uploadData.message === 'success') {
      const sessionId = uploadData.session_id;

      // Create initial session card
      const initialSessionData = {
        session_id: sessionId,
        tool_id: selectedTool,
        status: 'uploading',
        progress: 0,
        timestamp: new Date().toISOString()
      };
      addSessionCard(initialSessionData);

      // Start the tool processing (non-blocking)
      fetch('/runtool', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ session_id: sessionId })
      }).then(response => response.json())
        .then(runData => {
          console.log('Tool processing started:', runData);
        })
        .catch(error => {
          console.error('Error starting tool processing:', error);
        });

      // Clear form to allow new uploads
      uploadedFile = null;
      fileInput.value = '';

      // Re-enable run button
      runButton.disabled = false;
      runButton.textContent = 'Run';

      alert('Session started successfully! You can start another session.');

    } else {
      alert('Upload failed: ' + uploadData.message);
      runButton.disabled = false;
      runButton.textContent = 'Run';
    }
  } catch (error) {
    console.error("Error processing file:", error);
    alert('An error occurred while processing the file');
    runButton.disabled = false;
    runButton.textContent = 'Run';
  }
});

// Initialize performance display and load sessions on page load
document.addEventListener('DOMContentLoaded', () => {
  showPerformance();
  loadUserSessions();

  // Note: Periodic refresh is now handled dynamically in loadUserSessions()
  // based on whether there are active sessions or not
});

/* Login Page Specific Styles - Matching AutoSpace Theme */
.login-body {
  margin: 0;
  padding: 0;
  font-family: 'Montserrat', sans-serif;
  background-image: url('./background.png');
  background-repeat: no-repeat;
  background-size:cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: aliceblue;
}

.login-body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: -1;
}

.login-container {
  background: rgba(32, 32, 32, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  overflow: hidden;
  width: 100%;
  max-width: 450px;
  margin: 20px;
}

.login-header {
  background-color: black;
  color: aliceblue;
  padding: 30px;
  text-align: center;
  position: relative;
  border-bottom: 2px solid aliceblue;
}

.login-header .logo {
  font-size: 28px;
  font-weight: bolder;
  letter-spacing: 5px;
  margin-bottom: 10px;
  background: url('./logo.png') no-repeat center;
  background-size: contain;
  height: 40px;
  width: 200px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  text-indent: -9999px;
  overflow: hidden;
}

.login-icon {
  font-size: 40px;
  opacity: 0.8;
  color: aliceblue;
}

.login-form-container {
  padding: 40px 30px;
  background-color: black;
}

.login-title {
  font-size: 24px;
  font-weight: bolder;
  color: aliceblue;
  text-align: center;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.login-subtitle {
  color: rgba(240, 248, 255, 0.7);
  text-align: center;
  margin-bottom: 30px;
  font-size: 14px;
  letter-spacing: 0.5px;
}

.login-form, .register-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: bolder;
  color: aliceblue;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.form-label i {
  margin-right: 8px;
  width: 16px;
  color: #0f33ff;
}

.form-input {
  width: 100%;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  background: rgba(32, 32, 32, 0.05);
  color: aliceblue;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #0f33ff;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.form-input::placeholder {
  color: rgba(240, 248, 255, 0.7);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: rgba(240, 248, 255, 0.7);
  cursor: pointer;
  user-select: none;
  letter-spacing: 0.5px;
}

.checkbox-container input {
  margin-right: 8px;
}

.login-button {
  width: 100%;
  background-color: #0f33ff;
  color: aliceblue;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 15px 20px;
  border-radius: 2px;
  font-size: 14px;
  font-weight: bolder;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.login-button:hover {
  background-color: rgba(15, 51, 255, 0.8);
  border-color: aliceblue;
}

.login-button:active {
  transform: translateY(1px);
}

.login-footer {
  text-align: center;
  margin-top: 25px;
  font-size: 14px;
  color: rgba(240, 248, 255, 0.7);
  letter-spacing: 0.5px;
}

.login-footer a {
  color: #0f33ff;
  text-decoration: none;
  font-weight: bolder;
  letter-spacing: 0.5px;
}

.login-footer a:hover {
  color: aliceblue;
  text-decoration: underline;
}

.message-container {
  margin: 20px 30px;
  padding: 15px;
  border-radius: 2px;
  text-align: center;
  font-size: 14px;
  font-weight: bolder;
  letter-spacing: 0.5px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-container.success {
  background: rgba(32, 32, 32, 0.05);
  color: #0f33ff;
  border-color: #0f33ff;
}

.message-container.error {
  background: rgba(32, 32, 32, 0.05);
  color: #ff4444;
  border-color: #ff4444;
}

.register-form {
  animation: fadeIn 0.3s ease;
}

.login-form {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    margin: 10px;
    border-radius: 15px;
  }
  
  .login-header {
    padding: 25px 20px;
  }
  
  .login-header .logo {
    font-size: 24px;
  }
  
  .login-icon {
    font-size: 35px;
  }
  
  .login-form-container {
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 22px;
  }
}

/* Loading state */
.login-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.login-button.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid aliceblue;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

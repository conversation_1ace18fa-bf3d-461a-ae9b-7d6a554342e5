// Tool categories
const categories = [
  { id: "CAT001", name: "Extraction Tools", description: "Tools for extracting data from documents" },
  { id: "CAT002", name: "Comparison Tools", description: "Tools for comparing documents" },
  { id: "CAT003", name: "Search Tools", description: "Tools for searching within documents" },
  { id: "CAT004", name: "Analysis Tools", description: "Tools for analyzing document content" }
];

const toolsData = [
  {
    name: "Extractor",
    description: "Extracts text from the document line by line.",
    id: "T001",
    categoryId: "CAT001"
  },
  {
    name: "Acrobat Sim",
    description: "Compares two documents and underline the changes in the document while providing summary of the changes.",
    id: "T002",
    categoryId: "CAT002"
  },
  {
    name: "Meta Data",
    description: `Extracts meta data from the document:
    - Document title
    - Document creation date
    - Document modification date
    - Document author`,
    id: "T003",
    categoryId: "CAT001"
  },
  {
    name: "Keyword Search",
    description: `Searches for keywords in the document and returns the lines containing the keywords.`,
    id: "T004",
    categoryId: "CAT003"
  },
  {
    name: "Part Search",
    description: `Searches for parts [single part or list of parts] in the document and returns 4 Status:
    - FOUND EXACT: Part found exactly as it's in the document.
    - FOUND DIFFERENT FORMAT: part found after remoing special characters like spaces, dots, etc.
    - NOT FOUND: Part not found in the document.
    - FOUND NON APLHA: Part found with additional characters.`,
    id: "T005",
    categoryId: "CAT003"
  },
  {
    name: "Equal Manual",
    description: `> This tool is designed for the following purposes:
  1. Identify documents that are identical.
  2. Identify documents that are identical when dates are ignored.
  3. Notify users of potential reach or life cycle changes.
  4. Notify users of potentially unsearchable documents.
  5. Detect image changes based on the number of images in the document.
  6. Group documents based on:
     - Number of characters in the document.
     - Number of lines changed between the old and latest versions.`,
    id: "T006",
    categoryId: "CAT002"
  },
  {
    name: "Magic",
    description: `> This tool is designed to:
  1. Extract all lines containing keywords specified in "keywords.xlsx".
  2. Compare extracted lines to highlight changes.
  3. Map keywords to their corresponding features using "mapping.xlsx".
> The input file ("input.xlsx") contains entries grouped by vendor code.
> Keywords and mapped features are vendor-specific to prevent conflicts.
> The output is generated in separate files, each named after the corresponding supplier.

** Note: Each supplier must have an associated keyword file and mapping database.`,
    id: "T007",
    categoryId: "CAT004"
  },
  {
    name: "Image Compare",
    description: "Compares images between two documents and highlights changes by page and position.",
    id: "T008",
    categoryId: "CAT002"
  },
  {
    name: "Catalogue Compare",
    description: "Compares two documents and identifies changed pages while ignoring date differences.",
    id: "T009",
    categoryId: "CAT002"
  }
];

// Tool mapping functions
function getToolIdByName(toolName) {
  const tool = toolsData.find(t => t.name === toolName);
  return tool ? tool.id : toolName; // fallback to name if not found
}

function getToolNameById(toolId) {
  const tool = toolsData.find(t => t.id === toolId);
  return tool ? tool.name : toolId; // fallback to id if not found
}

function getToolsByCategory(categoryId) {
  return toolsData.filter(tool => tool.categoryId === categoryId);
}

function getCategoryById(categoryId) {
  return categories.find(cat => cat.id === categoryId);
}

// User ID generation function
function generateUserId(username) {
  // Simple hash function to generate consistent user ID
  let hash = 0;
  for (let i = 0; i < username.length; i++) {
    const char = username.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return 'U' + Math.abs(hash).toString().padStart(6, '0');
}

// Export the data for ES modules
export default toolsData;
export { categories, getToolIdByName, getToolNameById, getToolsByCategory, getCategoryById, generateUserId };
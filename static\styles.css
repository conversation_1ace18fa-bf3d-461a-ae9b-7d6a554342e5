/* Variables */
:root {
    /* Colors - Bluespace Theme */
    --color-bg: black;
    --color-text: aliceblue;
    --color-text-secondary: rgba(240, 248, 255, 0.7);
    --color-border: rgba(255, 255, 255, 0.2);
    --color-white: rgba(32, 32, 32, 0.05);
    --color-accent: #0f33ff;
    --color-card-bg: transparent;
    --color-card-border: aliceblue;

    /* Spacing */
    --spacing-xs: 5px;
    --spacing-sm: 8px;
    --spacing-md: 10px;
    --spacing-lg: 15px;
    --spacing-xl: 20px;
    --spacing-xxl: 30px;

    /* Typography */
    --font-size-xs: 10px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 42px;

    /* Layout */
    --border-standard: 1px solid var(--color-border);
    --box-padding: var(--spacing-lg);
    --letter-spacing-standard: 0.5px;
    --letter-spacing-wide: 5px;
    --section-gap: 0;
    --card-gap: var(--spacing-md);
    --box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    --border-radius-sm: 2px;
    --backdrop-blur: blur(20px);
}

/* Base styles */
* {
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    padding: 0;
}

body {
    background: url('./background.png') center/cover no-repeat fixed;
    /* Make sure:
    1. The image file exists in the static folder
    2. The path is relative to the CSS file location
    3. The image filename matches exactly including case sensitivity */
    color: var(--color-text);
    line-height: 1.6;
    margin: 0;
    min-height: 100vh;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

.container {
    display: grid;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    width: 100%;
}

/* Common styles */
.section-container {
    display: flex;
    flex-direction: column;
    gap: var(--section-gap);
    background-color: var(--color-bg);
}

.box {
    padding: var(--box-padding);
    background: var(--color-white);
    box-shadow: var(--box-shadow);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    border: var(--border-standard);
    margin: 10px;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}



.box-title {
    font-size: var(--font-size-md);
    font-weight: bolder;
    letter-spacing: var(--letter-spacing-standard);
    color: var(--color-text);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    padding-bottom: var(--spacing-xs);
    display: inline-block;
}

.secondary-text {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

/* Top Bar */
.top-bar {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 24px;
    background-color: var(--color-bg);
    letter-spacing: var(--letter-spacing-wide);
    border-bottom: 2px solid var(--color-card-border);
}

.logo {
    font-weight: bolder;
    font-size: larger;
    letter-spacing: var(--letter-spacing-wide);
    background: url('./logo.png') no-repeat center;
    background-size: contain;
    height: 40px;
    width: 200px;
    padding: 0;
    display: flex;
    align-items: center;
    text-indent: -9999px;
    overflow: hidden;
}

.top-menu {
    display: flex;
    gap:50px
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 90px;
}

nav ul li {
    margin-right: var(--spacing-xxl);
}

nav ul li a {
    text-decoration: none;
    color: var(--color-text);
    font-size: var(--font-size-md);
    letter-spacing: var(--letter-spacing-standard);
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--color-text-secondary);
}

.account {
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
}

/* Content Area */
.content {
    display: grid;
    grid-template-columns: 120px 500px 1.5fr 1fr;
    grid-template-rows: 1fr;
    min-height: 75vh;
    background-color: var(--color-bg);
    padding: 24px;
    align-items: center;
}

/* Side Bar */
.side-bar {
    margin-right: 20px;
    height: 600px;
    align-items: center;
    width: 70px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 100px;
    justify-content: center;
    background-image: linear-gradient(var(--color-bg) 33%, rgba(255,255,255,0) 0%);
    background-position: right;
    background-size: 1px 3px;
    background-repeat: repeat-y;
}

.tabs {
    display: flex;
    flex-direction: column;
    gap: 100px;
    width: 70px;
}

.tab {
    width: 60px;
    height: 60px;
    position: relative;
    right: -40px;
    border: 0;
    font-weight: normal;
    font-size: medium;
    background-color: transparent;
    color: var(--color-text);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab.active span {
    opacity: 1;
    transform: translate(-50%, -50%);
    width: 75px;
    background-color: var(--color-accent);
}

.tab span {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(100%, -50%);
    font-weight: bolder;
    letter-spacing: 0.5px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    width: 75px;
    height: 8px;
    font-size: var(--font-size-sm);
    
}

.tab:hover span {
    opacity: 1;
    transform: translate(-50%, -50%);
}

.tab .icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-text);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab:hover .icon {
    transform: scale(0);
}

.tab.active .icon {
    transform: scale(0);
}

/* Tool Cards Section */
.tools-section {
    transition: all 0.5s;
    display: flex;
    gap: 15px;
    flex-direction: column;
    padding: 24px;
    justify-content: flex-start;
    width: 100%;
    overflow: hidden;
}

.tool-card {
    transition: box-shadow 0.5s, transform 0.5s;
    border: 5px solid var(--color-card-border);
    width: 100%;
    max-width: 450px;
    height: 100px;
    padding: 15px;
    background-color: var(--color-card-bg);
    gap: var(--card-gap);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-md);
}

.tool-card:hover {
    cursor: pointer;
    box-shadow: 5px 5px 0 var(--color-accent);
    transform: translate(1px, 1px) rotate(2deg) scale(1.05);
}

.tool-card span {
    display: flex;
    align-items: center;
    gap: var(--card-gap);
    width : 100%;
}

.tool-card h3 {
    font-size: x-large;
    background-color: var(--color-accent);
    border-radius: 100%;
    width: 50px;
    height: 50px;
}

.tool-card p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}
.section h3{
    padding: 10px;
}

/* Middle Section */
.middle-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.section{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

.description-box .tool-description{
    margin:0px;

}

.description-box{
    justify-content: space-between;
}

.tool-name {
    font-weight: bold;
    margin-bottom: var(--spacing-md);
}

.tool-description {
    margin-bottom: var(--spacing-lg);
    white-space: pre-line;
}

.button-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}
.progress-section{
    flex-grow: 1;
}
.smaller-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.progress-box, .finished-box {
    flex-grow:  1;
    border-radius: var(--border-radius-sm);
}
.session-card{
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    border-left: 4px solid var(--color-accent);
    background: var(--color-white);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    margin-bottom: var(--spacing-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--border-radius-sm);
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.tool-name {
    font-weight: bolder;
    font-size: var(--font-size-sm);
    color: var(--color-text);
    letter-spacing: var(--letter-spacing-standard);
}

.session-indicator {
    font-size: var(--font-size-lg);
    font-weight: bold;
}

.session-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
}

.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-accent), #2563eb);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: var(--font-size-xs);
    font-weight: bolder;
    color: var(--color-text);
    min-width: 35px;
    text-align: right;
}

.click-copy {
    font-size: var(--font-size-xs);
    color: var(--color-accent);
    cursor: pointer;
    text-align: center;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background: rgba(15, 51, 255, 0.1);
    transition: all 0.2s ease;
}

.click-copy:hover {
    background: rgba(15, 51, 255, 0.2);
    color: var(--color-text);
}
.session-card:hover {
    cursor: pointer;
    border-color: var(--color-text);
    transform: translateX(5px);
}
.session-card:active{
    opacity: 0.7;
    transform: scale(0.98);
}

.output-file{
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.session-info, .click-copy{
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
}

/* Right Section */
.right-section {
    background-color: var(--color-bg);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.visualization-box {
    flex-grow: 1;
}

.visualization-box ul {
    list-style-position: inside;
    padding-left: var(--spacing-md);
}

.visualization-box ul li {
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.visualization-box ul li::marker {
    color: red;
}

.users-box {
    height: 180px;
    border-radius: var(--border-radius-sm);
}

.user-count {
    color: var(--color-text-secondary);
    text-align: center;
    padding: var(--spacing-xl) 0;
    font-style: italic;
}

/* Footer */
footer {
    padding: var(--spacing-lg);
    text-align: center;
    background-color: var(--color-bg);
    border-top: 2px solid var(--color-border);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.upload-button, .run-button, .download-sample-button {
    display: none;
    margin-top: 10px;
    margin-right: 8px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    color: var(--color-text);
    background-color: transparent;
    border: 2px solid var(--color-text);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--border-radius-sm);
    outline: none;
    font-weight: bolder;
    letter-spacing: 0.5px;
}

.mode-container {
    display: none;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: 10px;
    margin-right: 8px;
    padding: 5px 10px;
    border: 2px solid var(--color-text);
    border-radius: var(--border-radius-sm);
    background-color: transparent;
}

.upload-button:hover, .run-button:hover, .download-sample-button:hover {
    background-color: var(--color-accent);
    color: var(--color-text);
    border-color: var(--color-accent);
    transform: scale(1.05);
}

.upload-button:active, .run-button:active, .download-sample-button:active {
    transform: scale(0.95);
}

/* Blinking animation for status indicators */
.blink {
    animation: blink-animation 1.5s infinite;
}

@keyframes blink-animation {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* Scrollable content areas */
.scrollable-content {
    overflow-y: auto;
    max-height: 100%;
}

/* Sessions container layout - side by side */
.sessions-container {
    display: flex;
    flex-direction: row;
    height: 100%;
    gap: var(--spacing-md);
}

.active-sessions-section,
.finished-sessions-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Important for flex children to shrink */
}

.section-title {
    font-size: var(--font-size-sm);
    font-weight: bold;
    color: var(--color-text);
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
}

.active-sessions-section .box,
.finished-sessions-section .box {
    flex: 1;
    min-height: 0;
    overflow: hidden;
}

/* Tools section scrollable */
.tools-section {
    overflow: hidden;
}

.tools-content {
    height: 100%;
    padding: var(--spacing-md);
}

/* AutoSpace section layout */
.description-section {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.autospace-header {
    flex-shrink: 0;
    padding: var(--spacing-md);
    border-bottom: var(--border-standard);
}

.description-box {
    flex: 1;
    min-height: 0;
    overflow: hidden;
    margin: 0;
}

.tool-description-content {
    height: 100%;
    padding: var(--spacing-md);
}

.autospace-footer {
    flex-shrink: 0;
    padding: var(--spacing-md);
    border-top: var(--border-standard);
}

/* Mode container styling */
.mode-button {
    margin: 0;
}

.mode-label {
    font-size: var(--font-size-sm);
    color: var(--color-text);
    cursor: pointer;
}

/* Session card status borders */
.session-card {
    border-left: 4px solid transparent;
    transition: border-color 0.3s ease;
}

.session-card.status-running {
    border-left-color: #3b82f6;
    animation: border-blink 1.5s infinite;
}

.session-card.status-done {
    border-left-color: #10b981;
}

.session-card.status-error {
    border-left-color: #ef4444;
    animation: border-blink 1.5s infinite;
}

.session-card.status-pending {
    border-left-color: #f59e0b;
    animation: border-blink 1.5s infinite;
}

/* Border blinking animation */
@keyframes border-blink {
    0%, 50% {
        border-left-color: currentColor;
    }
    51%, 100% {
        border-left-color: rgba(255, 255, 255, 0.3);
    }
}
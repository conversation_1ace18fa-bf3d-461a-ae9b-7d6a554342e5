<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Optimized Layout</title>
  <link rel="stylesheet" href="../static/styles.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap">
</head>
<body>
  <div class="container">
    <!-- Top Bar -->
    <header class="top-bar">
      <div class="logo">BlueSpace</div>
      <div class="top-menu">
        <nav>
          <ul>
            <li><a href="#">Home</a></li>
            <li><a href="#">AutoSpace</a></li>
            <li><a href="#">About</a></li>
            <li><a href="/logout">Logout</a></li>
          </ul>
        </nav>
      </div>
      <div class="fas fa-user-astronaut account" style="font-size: 30px;"></div>
    </header>

    <!-- Main Content Area -->
    <div class="content">
      <!-- Side Bar -->
      <aside class="side-bar">
        <div class="tabs">
          <div class="tab" id="CAT001">
            <i class="fas fa-file-export icon"></i>
            <span>EXTRACTION</span>
          </div>
          <div class="tab" id="CAT002">
            <i class="fas fa-compress-alt icon"></i>
            <span>COMPARISON</span>
          </div>
          <div class="tab" id="CAT003">
            <i class="fas fa-search icon"></i>
            <span>SEARCH</span>
          </div>
          <div class="tab" id="CAT004">
            <i class="fas fa-chart-line icon"></i>
            <span>ANALYSIS</span>
          </div>
        </div>
      </aside>

      <!-- Tool Cards Section -->
      <section class="tools-section section-container">
        <div class="tools-content scrollable-content">
          <!-- Tool cards will be populated here -->
        </div>
      </section>

      <!-- Middle Section -->
      <section class="middle-section section-container">
        <div class="description-section section">
          <div class="autospace-header">
            <h3 class="box-title">AutoSpace</h3>
            <p class="tool-name"></p>
          </div>

          <div class="description-box box">
            <div class="tool-description-content scrollable-content">
              <p class="tool-description"></p>
            </div>
          </div>

          <div class="autospace-footer">
            <div class="button-container">
              <label for="file-input" class="upload-button d-buttons" id="upload">Upload</label>
              <input type="file" id="file-input" style="display:none;" />

              <button class="run-button d-buttons" id="run">Run</button>
              <button class="download-sample-button d-buttons" id="download">Download Sample</button>

              <div class="mode-container d-buttons">
                <input type="checkbox" class="mode-button d-buttons" id="run-mode" />
                <label for="run-mode" class="mode-label">Fast Mode</label>
              </div>
            </div>
          </div>
        </div>
 
        <div class="progress-section section">
          <h3 class="box-title">Sessions Status</h3>
          <div class="sessions-container">
            <div class="active-sessions-section">
              <h4 class="section-title">Active Sessions</h4>
              <div class="progress-box box scrollable-content">
                <!-- Active sessions will be populated here -->
              </div>
            </div>
            <div class="finished-sessions-section">
              <h4 class="section-title">Finished Sessions</h4>
              <div class="finished-box box scrollable-content">
                <!-- Finished sessions will be populated here -->
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Right Section -->
      <section class="right-section section-container">
        <div class="visualization-section section">
          <h3 class="box-title">performance</h3>
          <div class="visualization-box box"></div>
        </div>
        <div class="users-section section">
          <h3 class="box-title">active users</h3>
          <div class="users-box box"></div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer>
      <div class="copyright secondary-text">
        © <EMAIL> All rights reserved.
      </div>
    </footer>
  </div>

  <script type="module" src="../static/app.js"></script>
</body>
</html>
